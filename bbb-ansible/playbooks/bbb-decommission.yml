---
# Clean BBB Server Decommissioning Playbook
- name: Decommission BBB server gracefully
  hosts: all
  become: yes
  gather_facts: yes
  vars:
    force_decommission: "{{ force_decommission | default(false) }}"

  tasks:
    # Check for active meetings before proceeding
    - name: Check for active meetings
      shell: "bbb-conf --check | grep -E 'Current meetings:|Users currently in:' | head -2"
      register: active_meetings_check
      changed_when: false
      failed_when: false

    - name: Parse meeting status
      set_fact:
        current_meetings: "{{ (active_meetings_check.stdout | regex_search('Current meetings:\\s*(\\d+)', '\\1') | default(['0'], true) | first) | int }}"
        current_users: "{{ (active_meetings_check.stdout | regex_search('Users currently in:\\s*(\\d+)', '\\1') | default(['0'], true) | first) | int }}"

    - name: Display meeting status
      debug:
        msg: "Meeting status: {{ current_meetings }} meetings, {{ current_users }} users"

    - name: Warn about active meetings
      debug:
        msg: "⚠️ Warning: {{ current_meetings }} active meetings with {{ current_users }} users"
      when:
        - (current_meetings | int > 0 or current_users | int > 0)
        - not (force_decommission | bool)

    # Set server information for consistent use
    - name: Set server information
      set_fact:
        server_id: "{{ server_id | default(inventory_hostname) }}"
        # Construct server URL - dns_name should be the full subdomain (e.g., bbb-medium-123)
        # If dns_name is provided, use it + base domain, otherwise fallback to server_id + base domain
        server_url: "https://{{ dns_name | default(server_id | default(inventory_hostname)) }}.{{ base_domain | default('geerd.net') }}/bigbluebutton/api"

    - name: Display decommission information
      debug:
        msg:
          - "Decommissioning server: {{ server_id }}"
          - "Server URL: {{ server_url }}"
          - "DNS Name: {{ dns_name | default('Using server_id fallback') }}"
          - "Force decommission: {{ force_decommission | bool }}"

    # Transfer recordings if any exist
    - name: Check for recordings to transfer
      find:
        paths: /var/bigbluebutton/recording/raw
        patterns: "*.tar"
        file_type: file
      register: recording_files

    - name: Display recording count
      debug:
        msg: "Found {{ recording_files.files | length }} recordings to transfer"

    - name: Transfer recordings to processing server
      include_role:
        name: bbb_recording
      when: recording_files.files | length > 0

    # Remove from Scalelite load balancer (CRITICAL - do this early and with retries)
    - name: Remove server from Scalelite load balancer
      include_role:
        name: scalelite
      vars:
        scalelite_action: "unregister"
      when: scalelite.enabled | default(true) | bool
      tags:
        - scalelite
        - always
      ignore_errors: no  # Make this critical - fail if it can't be removed

    # Only continue with server shutdown if not forced and meetings exist
    - name: Stop if active meetings exist and not forced
      fail:
        msg: "❌ Cannot decommission server with {{ current_meetings }} active meetings and {{ current_users }} users. Use force_decommission=true to override."
      when:
        - (current_meetings | int > 0 or current_users | int > 0)
        - not (force_decommission | bool)

    # Stop BBB services gracefully
    - name: Stop BigBlueButton services
      systemd:
        name: "{{ item }}"
        state: stopped
        enabled: no
      loop:
        - bbb-rap-resque-worker
        - bbb-rap-starter
        - bbb-web
        - bbb-webrtc-sfu
        - bbb-html5
        - nginx
        - freeswitch
        - redis-server
      ignore_errors: yes

    # Clean up recordings directory
    - name: Clean up local recordings
      file:
        path: /var/bigbluebutton/recording
        state: absent
      ignore_errors: yes

    # Final status message
    - name: Decommissioning complete
      debug:
        msg:
          - "✅ Server {{ server_id }} decommissioned successfully"
          - "� Status: {{ current_meetings | default(0) }} meetings, {{ current_users | default(0) }} users at start"
          - "📼 Recordings: {{ recording_files.files | default([]) | length }} files transferred"
          - "⚖️ Scalelite: Server disabled and removed from load balancer"
          - "🛑 Services: All BBB services stopped"
          - "🗑️ Cleanup: Local recordings and temp files removed"
          - "💀 Ready for infrastructure deletion"
