# BBB Recording and Processing Architecture

## Overview
This setup creates a clean separation between recording servers and processing servers:

- **Recording Servers**: Capture meetings and transfer raw files only
- **Processing Server**: Receives raw files, processes them into videos, and uploads to cloud

## Architecture Flow

```
Meeting End → Recording Server → Raw Files Transfer → Processing Server → Video Processing → Cloud Upload
```

## Recording Servers (bbb_recording role)

### What they do:
- ✅ Record BigBlueButton meetings
- ✅ Archive raw recording files
- ✅ Transfer raw files to processing server
- ❌ No local video processing (disabled)
- ❌ No local storage of processed videos

### Key files:
- `/usr/local/bigbluebutton/core/scripts/post_archive.rb` - B<PERSON> calls this after archiving
- `/usr/local/bigbluebutton/core/scripts/post_archive/upload_raw_recording.rb` - Transfers raw files

### Configuration:
- Local processing workers are stopped
- Only raw file transfer is enabled
- Transfers happen immediately when meetings end

## Processing Server (Manual Setup)

**Note**: The processing server is configured manually as a standalone server.

### What it does:
- ✅ Receives raw files from recording servers
- ✅ Extracts and processes them using BBB's built-in workflow
- ✅ Generates video files with `bbb-playbook-video`
- ✅ Uploads final videos to DigitalOcean Spaces
- ✅ Optionally integrates with API for metadata

### Key components:

#### 1. BBB Configuration (`bigbluebutton.yml`)
```yaml
steps:
  archive: 'sanity'
  sanity: 'captions'
  captions:
    - 'process:video'
  'process:video': 'publish:video'
```

#### 2. Raw File Processor (`/usr/local/bin/raw_processor.rb`)
- Monitors `/var/bigbluebutton/processing` for incoming tar files
- Extracts raw files to `/var/bigbluebutton/recording/raw/`
- Triggers BBB processing with `bbb-record --rebuild`
- Runs as systemd service: `raw-processor.service`

#### 3. Post-Publish Script (`/usr/local/bigbluebutton/core/scripts/post_publish/upload_video.rb`)
- Called by BBB after video processing completes
- Uploads final video to DigitalOcean Spaces
- Optionally calls API to record metadata

### Directory Structure:
```
/var/bigbluebutton/
├── processing/          # Incoming raw files (tar.gz)
├── recording/raw/       # Extracted raw files
├── published/video/     # Final processed videos
└── ...
```

## Configuration Variables

### Recording Server (`bbb_recording/defaults/main.yml`)
```yaml
video_processing_server:
  enabled: true
  ip: "************"           # Processing server IP
  user: "root"                 # SSH user
  password: "your_password"    # SSH password (or use ssh_key_path)
  transfer_path: "/var/bigbluebutton/processing"
```

### Processing Server (Manual Configuration)
Required manual setup:
- BigBlueButton installation
- S3cmd configuration for cloud storage
- Raw processor daemon installation
- Upload script configuration
  hasura_url: "https://api.example.com/v1/graphql"
  hasura_admin_secret: "your_secret"
```

## Deployment

### Using the integrated approach with bbb-provision.yml:

```bash
# Deploy recording servers (regular BBB servers with recording only)
ansible-playbook -i inventory/droplet.ini playbooks/bbb-provision.yml --tags recording --limit bbb_servers

# Deploy processing server (dedicated video processing)
ansible-playbook -i inventory/droplet.ini playbooks/bbb-provision.yml --tags processing --limit processing_servers

# Deploy everything for a specific server
ansible-playbook -i inventory/droplet.ini playbooks/bbb-provision.yml --limit bbb-processing-001
```

### Server Role Configuration

Set `server_role` variable in your inventory:
- `server_role=recording` - Regular BBB server that only records and transfers raw files
- `server_role=processing` - Dedicated server that processes videos and uploads to cloud

### Example inventory:
```ini
[bbb_servers]
bbb-server-1 ansible_host=******* server_role=recording
bbb-server-2 ansible_host=******* server_role=recording

[processing_servers]
bbb-processing-1 ansible_host=************ server_role=processing
```

## Benefits of This Architecture

1. **Scalability**: Multiple recording servers → One processing server
2. **Resource Efficiency**: Recording servers focus only on recording
3. **Centralized Processing**: All video processing happens in one place
4. **Clean Separation**: Easy to manage and troubleshoot
5. **Cost Effective**: Less processing power needed on recording servers

## Monitoring

### Logs to check:
- Recording servers: `/var/log/bigbluebutton/post_archive.log`, `/var/log/bigbluebutton/raw_transfer.log`
- Processing server: `/var/log/bigbluebutton/raw_processor.log`, `/var/log/bigbluebutton/post_publish.log`

### Services to monitor:
- Processing server: `systemctl status raw-processor`
- Processing server: `systemctl status bbb-rap-resque-worker`

## Troubleshooting

### Raw files not transferring:
1. Check SSH connectivity from recording server to processing server
2. Verify credentials in `bbb_recording/defaults/main.yml`
3. Check `/var/log/bigbluebutton/raw_transfer.log`

### Videos not processing:
1. Check if raw-processor service is running: `systemctl status raw-processor`
2. Verify `/var/bigbluebutton/processing` has incoming files
3. Check `/var/log/bigbluebutton/raw_processor.log`

### Videos not uploading:
1. Verify DigitalOcean Spaces credentials
2. Check s3cmd configuration: `/home/<USER>/.s3cfg`
3. Test upload manually: `s3cmd -c /home/<USER>/.s3cfg ls`
