#!/usr/bin/env ruby
# BBB RAW Recording Transfer Script - Simple and Clean
# Only transfers RAW recordings to processing server, no local processing

require 'fileutils'
require 'logger'
require 'optparse'

# Parse command line arguments - B<PERSON> calls this with -m meeting_id
meeting_id = nil
OptionParser.new do |opts|
  opts.on('-m', '--meeting-id MEETING_ID', 'Meeting ID to process') do |id|
    meeting_id = id
  end
end.parse!

# Fallback to positional argument if -m not used
meeting_id ||= ARGV[0]

if meeting_id.nil? || meeting_id.empty?
  puts "Usage: #{$0} -m <meeting_id> or #{$0} <meeting_id>"
  exit(1)
end

# Setup logging
log_dir = '/var/log/bigbluebutton'
FileUtils.mkdir_p(log_dir) unless Dir.exist?(log_dir)
logger = Logger.new("#{log_dir}/raw_transfer.log", 'weekly')
logger.level = Logger::INFO

logger.info("Starting RAW transfer for meeting: #{meeting_id}")

# RAW recording directory
raw_dir = "/var/bigbluebutton/recording/raw/#{meeting_id}"

unless Dir.exist?(raw_dir)
  logger.error("RAW recording directory not found: #{raw_dir}")
  exit(1)
end

logger.info("Found RAW recording: #{raw_dir}")

# Create tar archive
tar_file = "/tmp/#{meeting_id}_raw.tar.gz"

begin
  # Create simple tar archive of raw files
  unless system("tar -czf #{tar_file} -C /var/bigbluebutton/recording/raw #{meeting_id}")
    logger.error("Failed to create archive")
    exit(1)
  end

  logger.info("Created archive: #{tar_file}")

  # Transfer to processing server
  {% if video_processing_server.ssh_key_path is defined and video_processing_server.ssh_key_path != "" %}
  transfer_cmd = "scp -o StrictHostKeyChecking=no -i {{ video_processing_server.ssh_key_path }} #{tar_file} {{ video_processing_server.user }}@{{ video_processing_server.ip }}:{{ video_processing_server.transfer_path }}/"
  {% else %}
  transfer_cmd = "sshpass -p '{{ video_processing_server.password }}' scp -o StrictHostKeyChecking=no #{tar_file} {{ video_processing_server.user }}@{{ video_processing_server.ip }}:{{ video_processing_server.transfer_path }}/"
  {% endif %}

  logger.info("Transferring to processing server...")
  unless system(transfer_cmd)
    logger.error("Failed to transfer file")
    exit(1)
  end

  logger.info("Successfully transferred #{meeting_id} to processing server")

  # Verify transfer succeeded by checking file exists on remote server
  {% if video_processing_server.ssh_key_path is defined and video_processing_server.ssh_key_path != "" %}
  verify_cmd = "ssh -o StrictHostKeyChecking=no -i {{ video_processing_server.ssh_key_path }} {{ video_processing_server.user }}@{{ video_processing_server.ip }} 'test -f {{ video_processing_server.transfer_path }}/#{File.basename(tar_file)}'"
  {% else %}
  verify_cmd = "sshpass -p '{{ video_processing_server.password }}' ssh -o StrictHostKeyChecking=no {{ video_processing_server.user }}@{{ video_processing_server.ip }} 'test -f {{ video_processing_server.transfer_path }}/#{File.basename(tar_file)}'"
  {% endif %}

  unless system(verify_cmd)
    logger.error("Transfer verification failed - file not found on processing server")
    exit(1)
  end

  logger.info("Transfer verified successfully on processing server")

rescue => e
  logger.error("Transfer failed: #{e.message}")
  logger.error("Backtrace: #{e.backtrace.join("\n")}")
  exit(1)
ensure
  # Only clean up tar file, keep raw files until we're sure processing succeeded
  FileUtils.rm_f(tar_file) if File.exist?(tar_file)
  logger.info("Cleaned up temporary tar file")
end

logger.info("Transfer completed and verified successfully")
exit(0)
