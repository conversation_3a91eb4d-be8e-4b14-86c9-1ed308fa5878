---
# Transfer raw recording files to processing server when meeting ends

- name: Verify B<PERSON> is installed
  stat:
    path: /usr/bin/bbb-record
  register: bbb_installed
  become: true

- name: Fail if BBB not installed
  fail:
    msg: "BigBlueButton not installed. Run bbb role first."
  when: not bbb_installed.stat.exists

- name: Install transfer dependencies
  apt:
    name:
      - sshpass
    state: present
  become: true

- name: Create post-archive directory
  file:
    path: /usr/local/bigbluebutton/core/scripts/post_archive
    state: directory
    owner: bigbluebutton
    group: bigbluebutton
    mode: '0755'
  become: true

- name: Deploy transfer script
  template:
    src: upload_raw_recording.rb.j2
    dest: /usr/local/bigbluebutton/core/scripts/post_archive/upload_raw_recording.rb
    owner: bigbluebutton
    group: bigbluebutton
    mode: '0755'
  become: true

- name: Ensure post_archive directory exists
  file:
    path: /usr/local/bigbluebutton/core/scripts/post_archive
    state: directory
    owner: bigbluebutton
    group: bigbluebutton
    mode: '0755'
  become: true

- name: Deploy BBB post-archive hook script
  template:
    src: post_archive.rb.j2
    dest: /usr/local/bigbluebutton/core/scripts/post_archive/01-raw-transfer.rb
    owner: bigbluebutton
    group: bigbluebutton
    mode: '0755'
  become: true

- name: Verify post-archive hook deployment
  stat:
    path: /usr/local/bigbluebutton/core/scripts/post_archive/01-raw-transfer.rb
  register: post_archive_hook

- name: Display post-archive hook status
  debug:
    msg:
      - "Post-archive hook deployment status:"
      - "File exists: {{ post_archive_hook.stat.exists }}"
      - "File path: /usr/local/bigbluebutton/core/scripts/post_archive/01-raw-transfer.rb"
      - "Owner: {{ post_archive_hook.stat.pw_name | default('unknown') }}"
      - "Permissions: {{ post_archive_hook.stat.mode | default('unknown') }}"

- name: Test post-archive hook syntax
  shell: "ruby -c /usr/local/bigbluebutton/core/scripts/post_archive/01-raw-transfer.rb"
  register: syntax_check
  changed_when: false
  become: true

- name: Display syntax check result
  debug:
    msg: "Post-archive hook syntax check: {{ syntax_check.stdout | default('OK') }}"

- name: Enable BBB recording
  shell: bbb-record --enable
  become: true
  changed_when: false

- name: Disable local processing formats (we only transfer raw files)
  shell: "{{ item }}"
  loop:
    - bbb-record --disable video
    - bbb-record --disable presentation
  become: true
  changed_when: false
  ignore_errors: true

- name: Stop local recording workers (processing happens on dedicated server)
  systemd:
    name: "{{ item }}"
    state: stopped
    enabled: no
  loop:
    - bbb-rap-resque-worker
    - bbb-record-core
  become: true
  ignore_errors: true

- name: Display recording server configuration
  debug:
    msg:
      - "🎯 BBB Recording Server configured for RAW TRANSFER ONLY:"
      - "✅ Recording enabled, local processing disabled"
      - "❌ Local video/presentation processing: DISABLED"
      - "❌ Local recording workers: STOPPED"
      - "✅ Raw file transfer: ENABLED"
      - "📁 Post-archive hook: /usr/local/bigbluebutton/core/scripts/post_archive/01-raw-transfer.rb"
      - "📡 Processing server: {{ video_processing_server.ip }}"
      - "📁 Transfer path: {{ video_processing_server.transfer_path }}"
      - "📜 Transfer script: /usr/local/bigbluebutton/core/scripts/post_archive/upload_raw_recording.rb"
      - ""
      - "🚀 Raw files will be transferred immediately when meetings end!"

- name: List post-archive scripts that BBB will execute
  find:
    paths: /usr/local/bigbluebutton/core/scripts/post_archive
    patterns: "*.rb"
    file_type: file
  register: post_scripts
  become: true

- name: Display post-archive scripts
  debug:
    msg:
      - "Post-archive scripts that will be executed (in alphabetical order):"
      - "{{ post_scripts.files | map(attribute='path') | sort | list }}"
      - "Total scripts: {{ post_scripts.files | length }}"
