# DigitalOcean Configuration
digitalocean:
  api_token: "***********************************************************************"
  tag: "bbb"

# Core Settings
base_domain: "geerd.net"
email: "<EMAIL>"
organization: "geerd"  # Organization identifier for recordings


# Server types and their capacities (aligned with nest-app BBB scheduler)
server_types:
  medium:
    capacity: 160
    cpu: 8
    memory: 16
    jvm_heap: "2G"
    kurento_min_port: 32768
    kurento_max_port: 65535
  large:
    capacity: 320
    cpu: 16
    memory: 32
    jvm_heap: "4G"
    kurento_min_port: 32768
    kurento_max_port: 65535
  extra_large:
    capacity: 640
    cpu: 16
    memory: 64
    jvm_heap: "8G"
    kurento_min_port: 32768
    kurento_max_port: 65535

# BigBlueButton Settings
bbb:
  version: "jammy-300"  # BBB 3.0
  recording_enabled: true

# Scalelite Configuration
scalelite:
  enabled: true
  server_ip: "**************"
  server_user: "root"
  ssh_password: "Np66W3PnuK4j"  # SSH password for root user

# Storage Configuration (DigitalOcean Spaces)
storage:
  type: "spaces"
  enabled: true
  bucket_name: "bbb-terraform"
  region: "fra1"
  endpoint: "https://fra1.digitaloceanspaces.com"
  access_key: "DO801UPFGFY2WBYU7FYD"
  secret_key: "dS5dqoaxYTFRAO6j2rQRlKe8x9YHxdpAoOh9mWm+FFc"

# Video Processing Server
video_processing_server:
  enabled: true
  ip: "************"                    # IP of your processing server
  user: "root"                          # SSH user for transfer
  password: "Fv6J4OHUvH3J3IOj"          # SSH password (consider using ssh_key_path instead)
  # ssh_key_path: "/path/to/ssh/key"    # Alternative to password
  transfer_path: "/var/bigbluebutton/processing"
  trigger_processing: true

# SSL Configuration
ssl:
  use_letsencrypt: true
