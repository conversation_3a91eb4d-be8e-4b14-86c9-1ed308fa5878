# BBB Manager Dockerfile - Multi-stage build
FROM node:18-alpine AS builder

# Install system dependencies for building
RUN apk add --no-cache python3 py3-pip py3-virtualenv openssh-client sshpass

# Create app directory
WORKDIR /app

# Copy package files and install ALL dependencies (including dev)
COPY nest-app/package*.json ./
RUN npm ci

# Copy source code and build
COPY nest-app/ .
RUN npm run build

# Production stage
FROM node:18-alpine AS production

# Install system dependencies for Ansible and SSH
RUN apk add --no-cache \
    python3 \
    py3-pip \
    py3-virtualenv \
    openssh-client \
    sshpass \
    && python3 -m venv /opt/ansible-venv \
    && /opt/ansible-venv/bin/pip install ansible \
    && ln -s /opt/ansible-venv/bin/ansible* /usr/local/bin/

# Create app directory
WORKDIR /app

# Copy package files and install only production dependencies
COPY nest-app/package*.json ./
RUN npm ci --only=production && npm cache clean --force

# Copy built application from builder stage
COPY --from=builder /app/dist ./dist

# Copy Ansible configuration
COPY bbb-ansible/ ./bbb-ansible/

# Ensure proper permissions for Ansible files
RUN chmod -R 755 ./bbb-ansible

# Create a non-root user
RUN addgroup -g 1001 -S bbb-manager && \
    adduser -S bbb-manager -u 1001 -G bbb-manager

# Create necessary directories
RUN mkdir -p /app/logs /home/<USER>/.ssh && \
    chown -R bbb-manager:bbb-manager /app /home/<USER>

# Switch to non-root user
USER bbb-manager

# Set up SSH directory permissions
RUN chmod 700 /home/<USER>/.ssh

# Expose the application port
EXPOSE 3000

# Create startup script to handle SSH keys
RUN echo '#!/bin/sh' > /app/start.sh && \
    echo 'if [ -n "$SSH_PRIVATE_KEY_BASE64" ]; then' >> /app/start.sh && \
    echo '  echo "$SSH_PRIVATE_KEY_BASE64" | base64 -d > /home/<USER>/.ssh/id_rsa' >> /app/start.sh && \
    echo '  chmod 600 /home/<USER>/.ssh/id_rsa' >> /app/start.sh && \
    echo 'fi' >> /app/start.sh && \
    echo 'if [ -n "$SSH_PUBLIC_KEY_BASE64" ]; then' >> /app/start.sh && \
    echo '  echo "$SSH_PUBLIC_KEY_BASE64" | base64 -d > /home/<USER>/.ssh/id_rsa.pub' >> /app/start.sh && \
    echo '  chmod 644 /home/<USER>/.ssh/id_rsa.pub' >> /app/start.sh && \
    echo 'fi' >> /app/start.sh && \
    echo 'exec npm run start:prod' >> /app/start.sh && \
    chmod +x /app/start.sh

# Start the application with SSH key setup
CMD ["/app/start.sh"]
