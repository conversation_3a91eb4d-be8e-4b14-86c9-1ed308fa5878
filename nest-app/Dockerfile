# BBB Manager Dockerfile
FROM node:18-alpine

# Install system dependencies for Ansible and SSH
RUN apk add --no-cache \
    python3 \
    py3-pip \
    openssh-client \
    sshpass \
    && pip3 install ansible

# Create app directory
WORKDIR /app

# Copy package files first for better Docker layer caching
COPY package*.json ./

# Install Node.js dependencies
RUN npm ci --only=production

# Copy application source code
COPY . .

# Copy Ansible configuration from parent directory
COPY ../bbb-ansible ./bbb-ansible

# Build the application
RUN npm run build

# Create a non-root user
RUN addgroup -g 1001 -S bbb-manager && \
    adduser -S bbb-manager -u 1001 -G bbb-manager

# Create necessary directories
RUN mkdir -p /app/logs /home/<USER>/.ssh && \
    chown -R bbb-manager:bbb-manager /app /home/<USER>

# Switch to non-root user
USER bbb-manager

# Set up SSH directory permissions
RUN chmod 700 /home/<USER>/.ssh

# Expose the application port
EXPOSE 3000

# Start the application
CMD ["npm", "run", "start:prod"]
