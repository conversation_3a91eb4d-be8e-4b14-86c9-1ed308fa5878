# BBB Manager Dockerfile
FROM node:18-alpine

# Install system dependencies for Ansible and SSH
RUN apk add --no-cache \
    python3 \
    py3-pip \
    openssh-client \
    sshpass \
    && pip3 install ansible

# Create app directory
WORKDIR /app

# Copy package files first for better Docker layer caching
COPY package*.json ./

# Install Node.js dependencies
RUN npm ci --only=production

# Copy application source code
COPY . .

# Copy Ansible configuration from parent directory
COPY ../bbb-ansible ./bbb-ansible

# Ensure proper permissions for Ansible files
RUN chmod -R 755 ./bbb-ansible

# Build the application
RUN npm run build

# Create a non-root user
RUN addgroup -g 1001 -S bbb-manager && \
    adduser -S bbb-manager -u 1001 -G bbb-manager

# Create necessary directories
RUN mkdir -p /app/logs /home/<USER>/.ssh && \
    chown -R bbb-manager:bbb-manager /app /home/<USER>

# Switch to non-root user
USER bbb-manager

# Set up SSH directory permissions
RUN chmod 700 /home/<USER>/.ssh

# Expose the application port
EXPOSE 3000

# Create startup script to handle SSH keys
RUN echo '#!/bin/sh' > /app/start.sh && \
    echo 'if [ -n "$SSH_PRIVATE_KEY_BASE64" ]; then' >> /app/start.sh && \
    echo '  echo "$SSH_PRIVATE_KEY_BASE64" | base64 -d > /home/<USER>/.ssh/id_rsa' >> /app/start.sh && \
    echo '  chmod 600 /home/<USER>/.ssh/id_rsa' >> /app/start.sh && \
    echo 'fi' >> /app/start.sh && \
    echo 'if [ -n "$SSH_PUBLIC_KEY_BASE64" ]; then' >> /app/start.sh && \
    echo '  echo "$SSH_PUBLIC_KEY_BASE64" | base64 -d > /home/<USER>/.ssh/id_rsa.pub' >> /app/start.sh && \
    echo '  chmod 644 /home/<USER>/.ssh/id_rsa.pub' >> /app/start.sh && \
    echo 'fi' >> /app/start.sh && \
    echo 'exec npm run start:prod' >> /app/start.sh && \
    chmod +x /app/start.sh

# Start the application with SSH key setup
CMD ["/app/start.sh"]
