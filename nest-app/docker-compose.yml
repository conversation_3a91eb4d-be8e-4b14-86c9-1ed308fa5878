version: '3.8'

services:
  bbb-manager:
    build: .
    container_name: bbb-manager-app
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - REDIS_URL=redis://redis:6379
      # Add your environment variables here:
      # - DIGITALOCEAN_API_TOKEN=your_token
      # - HASURA_GRAPHQL_ENDPOINT=your_endpoint
      # - HASURA_GRAPHQL_ADMIN_SECRET=your_secret
      # - DO_SPACES_ACCESS_KEY=your_key
      # - DO_SPACES_SECRET_KEY=your_secret
      # - DO_SPACES_BUCKET=your_bucket
      # - DO_SPACES_REGION=fra1
      # - DO_SPACES_ENDPOINT=https://fra1.digitaloceanspaces.com
    depends_on:
      redis:
        condition: service_healthy
    volumes:
      # Mount SSH keys for Ansible (create this directory on host)
      - ~/.ssh:/home/<USER>/.ssh:ro
      # Mount logs directory
      - ./logs:/app/logs
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    container_name: bbb-manager-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

volumes:
  redis_data:
    driver: local
